'use client'

import {
  Settings as SettingsIcon,
  User,
  Store,
  Palette,
  Bell,
  Shield,
  Database,
  Globe,
  Save,
  RefreshCw,
  Moon,
  Sun,
  Monitor,
  Check,
  X,
  Info,
  AlertTriangle
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/components'

interface SettingsSection {
  id: string
  label: string
  icon: any
  description: string
}

interface StoreSettings {
  storeName: string
  storeDescription: string
  storeAddress: string
  storePhone: string
  storeEmail: string
  currency: string
  timezone: string
  language: string
}

interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  lowStockAlerts: boolean
  paymentReminders: boolean
  systemUpdates: boolean
}

interface SecuritySettings {
  twoFactorAuth: boolean
  sessionTimeout: number
  passwordExpiry: number
  loginAttempts: number
}

export default function Settings() {
  const { resolvedTheme, setTheme } = useTheme()
  const { user } = useAuth()
  const { addToast } = useToast()
  const [activeSection, setActiveSection] = useState('store')
  const [mounted, setMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Settings state
  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    storeName: 'Revantad Store',
    storeDescription: 'Your friendly neighborhood sari-sari store',
    storeAddress: '',
    storePhone: '',
    storeEmail: '',
    currency: 'PHP',
    timezone: 'Asia/Manila',
    language: 'en'
  })

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    lowStockAlerts: true,
    paymentReminders: true,
    systemUpdates: true
  })

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5
  })

  useEffect(() => {
    setMounted(true)
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load settings from localStorage or API
      const savedStoreSettings = localStorage.getItem('store-settings')
      const savedNotificationSettings = localStorage.getItem('notification-settings')
      const savedSecuritySettings = localStorage.getItem('security-settings')

      if (savedStoreSettings) {
        setStoreSettings(JSON.parse(savedStoreSettings))
      }
      if (savedNotificationSettings) {
        setNotificationSettings(JSON.parse(savedNotificationSettings))
      }
      if (savedSecuritySettings) {
        setSecuritySettings(JSON.parse(savedSecuritySettings))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const saveSettings = async () => {
    setIsLoading(true)
    try {
      // Save to localStorage (in a real app, this would be an API call)
      localStorage.setItem('store-settings', JSON.stringify(storeSettings))
      localStorage.setItem('notification-settings', JSON.stringify(notificationSettings))
      localStorage.setItem('security-settings', JSON.stringify(securitySettings))

      addToast({
        type: 'success',
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully.'
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      addToast({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save settings. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const settingsSections: SettingsSection[] = [
    {
      id: 'store',
      label: 'Store Information',
      icon: Store,
      description: 'Manage your store details and business information'
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: Palette,
      description: 'Customize the look and feel of your dashboard'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Configure notification preferences and alerts'
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Manage security settings and access controls'
    },
    {
      id: 'system',
      label: 'System',
      icon: Database,
      description: 'System preferences and advanced configurations'
    }
  ]

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-green-600" />
      </div>
    )
  }

  const renderStoreSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Store Name
          </label>
          <input
            type="text"
            value={storeSettings.storeName}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, storeName: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Phone Number
          </label>
          <input
            type="tel"
            value={storeSettings.storePhone}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, storePhone: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Store Description
          </label>
          <textarea
            value={storeSettings.storeDescription}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, storeDescription: e.target.value }))}
            rows={3}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Store Address
          </label>
          <textarea
            value={storeSettings.storeAddress}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, storeAddress: e.target.value }))}
            rows={2}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Email Address
          </label>
          <input
            type="email"
            value={storeSettings.storeEmail}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, storeEmail: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Currency
          </label>
          <select
            value={storeSettings.currency}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, currency: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          >
            <option value="PHP">Philippine Peso (₱)</option>
            <option value="USD">US Dollar ($)</option>
            <option value="EUR">Euro (€)</option>
          </select>
        </div>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4" style={{
          color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
        }}>
          Theme Preferences
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {[
            { value: 'light', label: 'Light', icon: Sun },
            { value: 'dark', label: 'Dark', icon: Moon },
            { value: 'system', label: 'System', icon: Monitor }
          ].map((theme) => {
            const Icon = theme.icon
            const isSelected = resolvedTheme === theme.value

            return (
              <button
                key={theme.value}
                onClick={() => setTheme(theme.value)}
                className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                  isSelected
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-green-300'
                }`}
                style={{
                  backgroundColor: isSelected
                    ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)')
                    : (resolvedTheme === 'dark' ? '#374151' : '#ffffff')
                }}
              >
                <Icon className={`h-6 w-6 mx-auto mb-2 ${
                  isSelected ? 'text-green-600' : 'text-gray-500'
                }`} />
                <div className={`font-medium ${
                  isSelected ? 'text-green-600' : (resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-700')
                }`}>
                  {theme.label}
                </div>
              </button>
            )
          })}
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        {[
          { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },
          { key: 'pushNotifications', label: 'Push Notifications', description: 'Browser push notifications' },
          { key: 'lowStockAlerts', label: 'Low Stock Alerts', description: 'Get notified when products are running low' },
          { key: 'paymentReminders', label: 'Payment Reminders', description: 'Reminders for customer debt payments' },
          { key: 'systemUpdates', label: 'System Updates', description: 'Notifications about system updates and maintenance' }
        ].map((setting) => (
          <div key={setting.key} className="flex items-center justify-between p-4 rounded-lg border" style={{
            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
            borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
          }}>
            <div>
              <h4 className="font-medium" style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}>
                {setting.label}
              </h4>
              <p className="text-sm" style={{
                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
              }}>
                {setting.description}
              </p>
            </div>
            <button
              onClick={() => setNotificationSettings(prev => ({
                ...prev,
                [setting.key]: !prev[setting.key as keyof NotificationSettings]
              }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notificationSettings[setting.key as keyof NotificationSettings]
                  ? 'bg-green-600'
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notificationSettings[setting.key as keyof NotificationSettings]
                    ? 'translate-x-6'
                    : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        ))}
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 rounded-lg border" style={{
          backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
          borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
        }}>
          <div>
            <h4 className="font-medium" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Two-Factor Authentication
            </h4>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Add an extra layer of security to your account
            </p>
          </div>
          <button
            onClick={() => setSecuritySettings(prev => ({ ...prev, twoFactorAuth: !prev.twoFactorAuth }))}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              securitySettings.twoFactorAuth ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-600'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                securitySettings.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Session Timeout (minutes)
            </label>
            <input
              type="number"
              value={securitySettings.sessionTimeout}
              onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) || 30 }))}
              min="5"
              max="480"
              className="w-full px-4 py-2 rounded-lg border transition-colors"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Max Login Attempts
            </label>
            <input
              type="number"
              value={securitySettings.loginAttempts}
              onChange={(e) => setSecuritySettings(prev => ({ ...prev, loginAttempts: parseInt(e.target.value) || 5 }))}
              min="3"
              max="10"
              className="w-full px-4 py-2 rounded-lg border transition-colors"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="p-4 rounded-lg border" style={{
        backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
        borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
      }}>
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-700 dark:text-blue-300">System Information</h4>
            <div className="mt-2 space-y-1 text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              <p>Version: 1.0.0</p>
              <p>Last Updated: {new Date().toLocaleDateString()}</p>
              <p>Database: Connected</p>
              <p>Storage: Cloudinary</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Timezone
          </label>
          <select
            value={storeSettings.timezone}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, timezone: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          >
            <option value="Asia/Manila">Asia/Manila</option>
            <option value="UTC">UTC</option>
            <option value="America/New_York">America/New_York</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2" style={{
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
          }}>
            Language
          </label>
          <select
            value={storeSettings.language}
            onChange={(e) => setStoreSettings(prev => ({ ...prev, language: e.target.value }))}
            className="w-full px-4 py-2 rounded-lg border transition-colors"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          >
            <option value="en">English</option>
            <option value="fil">Filipino</option>
            <option value="es">Español</option>
          </select>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'store':
        return renderStoreSettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return renderSecuritySettings()
      case 'system':
        return renderSystemSettings()
      default:
        return renderStoreSettings()
    }
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Settings Navigation */}
        <div className="lg:w-1/4">
          <div className="sticky top-8">
            <nav className="space-y-2">
              {settingsSections.map((section) => {
                const Icon = section.icon
                const isActive = activeSection === section.id

                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                      isActive
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700 border border-transparent'
                    }`}
                    style={{
                      backgroundColor: isActive
                        ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.15)' : 'rgba(34, 197, 94, 0.1)')
                        : 'transparent'
                    }}
                  >
                    <Icon className={`h-5 w-5 ${
                      isActive ? 'text-green-600' : 'text-gray-500'
                    }`} />
                    <div className="flex-1">
                      <div className={`font-medium ${
                        isActive
                          ? 'text-green-700 dark:text-green-300'
                          : (resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-700')
                      }`}>
                        {section.label}
                      </div>
                      <div className={`text-xs ${
                        isActive
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-gray-500'
                      }`}>
                        {section.description}
                      </div>
                    </div>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:w-3/4">
          <div className="rounded-xl border shadow-sm" style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}>
            <div className="p-6 border-b" style={{
              borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
            }}>
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold" style={{
                    color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                  }}>
                    {settingsSections.find(s => s.id === activeSection)?.label}
                  </h2>
                  <p className="text-sm mt-1" style={{
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}>
                    {settingsSections.find(s => s.id === activeSection)?.description}
                  </p>
                </div>
                <button
                  onClick={saveSettings}
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isLoading ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </div>
            </div>

            <div className="p-6">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
