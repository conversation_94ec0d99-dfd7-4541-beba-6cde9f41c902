'use client'

import {
  Settings as SettingsIcon,
  User,
  Store,
  Palette,
  Bell,
  Shield,
  Database,
  Globe,
  Save,
  RefreshCw,
  Moon,
  Sun,
  Monitor,
  Check,
  X,
  Info,
  AlertTriangle,
  ChevronRight,
  Sparkles,
  Lock,
  Eye,
  EyeOff,
  Download,
  Upload,
  Trash2,
  Copy,
  ExternalLink
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/components'

interface SettingsSection {
  id: string
  label: string
  icon: any
  description: string
}

interface StoreSettings {
  storeName: string
  storeDescription: string
  storeAddress: string
  storePhone: string
  storeEmail: string
  currency: string
  timezone: string
  language: string
}

interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  lowStockAlerts: boolean
  paymentReminders: boolean
  systemUpdates: boolean
}

interface SecuritySettings {
  twoFactorAuth: boolean
  sessionTimeout: number
  passwordExpiry: number
  loginAttempts: number
}

export default function Settings() {
  const { resolvedTheme, setTheme } = useTheme()
  const { user } = useAuth()
  const { addToast } = useToast()
  const [activeSection, setActiveSection] = useState('store')
  const [mounted, setMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Settings state
  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    storeName: 'Revantad Store',
    storeDescription: 'Your friendly neighborhood sari-sari store',
    storeAddress: '',
    storePhone: '',
    storeEmail: '',
    currency: 'PHP',
    timezone: 'Asia/Manila',
    language: 'en'
  })

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    lowStockAlerts: true,
    paymentReminders: true,
    systemUpdates: true
  })

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5
  })

  useEffect(() => {
    setMounted(true)
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load settings from localStorage or API
      const savedStoreSettings = localStorage.getItem('store-settings')
      const savedNotificationSettings = localStorage.getItem('notification-settings')
      const savedSecuritySettings = localStorage.getItem('security-settings')

      if (savedStoreSettings) {
        setStoreSettings(JSON.parse(savedStoreSettings))
      }
      if (savedNotificationSettings) {
        setNotificationSettings(JSON.parse(savedNotificationSettings))
      }
      if (savedSecuritySettings) {
        setSecuritySettings(JSON.parse(savedSecuritySettings))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const saveSettings = async () => {
    setIsLoading(true)
    try {
      // Save to localStorage (in a real app, this would be an API call)
      localStorage.setItem('store-settings', JSON.stringify(storeSettings))
      localStorage.setItem('notification-settings', JSON.stringify(notificationSettings))
      localStorage.setItem('security-settings', JSON.stringify(securitySettings))

      addToast({
        type: 'success',
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully.'
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      addToast({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save settings. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const settingsSections: SettingsSection[] = [
    {
      id: 'store',
      label: 'Store Information',
      icon: Store,
      description: 'Manage your store details and business information'
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: Palette,
      description: 'Customize the look and feel of your dashboard'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Configure notification preferences and alerts'
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Manage security settings and access controls'
    },
    {
      id: 'system',
      label: 'System',
      icon: Database,
      description: 'System preferences and advanced configurations'
    }
  ]

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-green-600" />
      </div>
    )
  }

  const renderStoreSettings = () => (
    <div className="space-y-8">
      {/* Store Identity Section */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600">
            <Store className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Store Identity
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Configure your store's basic information and branding
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
            }}>
              Store Name
            </label>
            <div className="relative">
              <input
                type="text"
                value={storeSettings.storeName}
                onChange={(e) => setStoreSettings(prev => ({ ...prev, storeName: e.target.value }))}
                className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                placeholder="Enter your store name"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <Sparkles className="h-4 w-4 text-green-500" />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Phone Number
            </label>
            <input
              type="tel"
              value={storeSettings.storePhone}
              onChange={(e) => setStoreSettings(prev => ({ ...prev, storePhone: e.target.value }))}
              className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="+63 XXX XXX XXXX"
            />
          </div>

          <div className="md:col-span-2 space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Store Description
            </label>
            <textarea
              value={storeSettings.storeDescription}
              onChange={(e) => setStoreSettings(prev => ({ ...prev, storeDescription: e.target.value }))}
              rows={4}
              className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500 resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="Describe your store and what makes it special..."
            />
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-green-500 to-teal-600">
            <Globe className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Contact Information
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              How customers can reach you
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

          <div className="md:col-span-2 space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Store Address
            </label>
            <textarea
              value={storeSettings.storeAddress}
              onChange={(e) => setStoreSettings(prev => ({ ...prev, storeAddress: e.target.value }))}
              rows={3}
              className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500 resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="Complete store address with barangay, city, province..."
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Email Address
            </label>
            <input
              type="email"
              value={storeSettings.storeEmail}
              onChange={(e) => setStoreSettings(prev => ({ ...prev, storeEmail: e.target.value }))}
              className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Currency
            </label>
            <div className="relative">
              <select
                value={storeSettings.currency}
                onChange={(e) => setStoreSettings(prev => ({ ...prev, currency: e.target.value }))}
                className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-500/20 focus:border-green-500 appearance-none"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <option value="PHP">Philippine Peso (₱)</option>
                <option value="USD">US Dollar ($)</option>
                <option value="EUR">Euro (€)</option>
              </select>
              <ChevronRight className="absolute right-3 top-1/2 transform -translate-y-1/2 rotate-90 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-8">
      {/* Theme Selection */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-purple-500 to-pink-600">
            <Palette className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Theme Preferences
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Choose your preferred color scheme
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {[
            {
              value: 'light',
              label: 'Light Mode',
              icon: Sun,
              description: 'Clean and bright interface',
              gradient: 'from-yellow-400 to-orange-500'
            },
            {
              value: 'dark',
              label: 'Dark Mode',
              icon: Moon,
              description: 'Easy on the eyes',
              gradient: 'from-slate-600 to-slate-800'
            },
            {
              value: 'system',
              label: 'System',
              icon: Monitor,
              description: 'Follows device settings',
              gradient: 'from-blue-500 to-purple-600'
            }
          ].map((theme) => {
            const Icon = theme.icon
            const isSelected = resolvedTheme === theme.value

            return (
              <button
                key={theme.value}
                onClick={() => setTheme(theme.value)}
                className={`group relative p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg ${
                  isSelected
                    ? 'border-green-500 shadow-lg shadow-green-500/25'
                    : 'border-gray-200 dark:border-gray-600 hover:border-green-300'
                }`}
                style={{
                  backgroundColor: isSelected
                    ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.15)' : 'rgba(34, 197, 94, 0.08)')
                    : (resolvedTheme === 'dark' ? '#4b5563' : '#f9fafb'),
                  borderColor: isSelected
                    ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')
                    : (resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb')
                }}
              >
                {isSelected && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <Check className="h-2.5 w-2.5 text-white" />
                  </div>
                )}

                <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${theme.gradient} flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-200`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>

                <div className={`font-semibold text-center mb-1 text-sm ${
                  isSelected
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-800 dark:text-gray-100'
                }`}>
                  {theme.label}
                </div>

                <div className={`text-xs text-center ${
                  isSelected
                    ? 'text-green-500 dark:text-green-300'
                    : 'text-gray-600 dark:text-gray-300'
                }`}>
                  {theme.description}
                </div>
              </button>
            )
          })}
        </div>
      </div>

      {/* Display Preferences */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-indigo-500 to-blue-600">
            <Monitor className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Display Preferences
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Customize your viewing experience
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 rounded-xl border" style={{
            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
            borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm" style={{
                  color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                }}>
                  Compact Mode
                </h4>
                <p className="text-xs" style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}>
                  Reduce spacing for more content
                </p>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-600 transition-colors">
                <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
              </button>
            </div>
          </div>

          <div className="p-4 rounded-xl border" style={{
            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
            borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm" style={{
                  color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                }}>
                  Animations
                </h4>
                <p className="text-xs" style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}>
                  Enable smooth transitions
                </p>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-green-600 transition-colors">
                <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-8">
      {/* Notification Preferences */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-orange-500 to-red-600">
            <Bell className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Notification Preferences
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Choose how you want to be notified
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {[
            {
              key: 'emailNotifications',
              label: 'Email Notifications',
              description: 'Receive notifications via email',
              icon: '📧',
              color: 'from-blue-500 to-cyan-600'
            },
            {
              key: 'pushNotifications',
              label: 'Push Notifications',
              description: 'Browser push notifications',
              icon: '🔔',
              color: 'from-purple-500 to-indigo-600'
            },
            {
              key: 'lowStockAlerts',
              label: 'Low Stock Alerts',
              description: 'Get notified when products are running low',
              icon: '📦',
              color: 'from-yellow-500 to-orange-600'
            },
            {
              key: 'paymentReminders',
              label: 'Payment Reminders',
              description: 'Reminders for customer debt payments',
              icon: '💰',
              color: 'from-green-500 to-emerald-600'
            },
            {
              key: 'systemUpdates',
              label: 'System Updates',
              description: 'Notifications about system updates and maintenance',
              icon: '⚙️',
              color: 'from-gray-500 to-slate-600'
            }
          ].map((setting) => {
            const isEnabled = notificationSettings[setting.key as keyof NotificationSettings]

            return (
              <div
                key={setting.key}
                className={`group relative p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                  isEnabled
                    ? 'border-green-500 shadow-md shadow-green-500/10'
                    : 'border-gray-200 dark:border-gray-600 hover:border-green-300'
                }`}
                style={{
                  backgroundColor: isEnabled
                    ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.05)' : 'rgba(34, 197, 94, 0.02)')
                    : (resolvedTheme === 'dark' ? '#374151' : '#ffffff')
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${setting.color} flex items-center justify-center text-sm group-hover:scale-110 transition-transform duration-200`}>
                      {setting.icon}
                    </div>
                    <div>
                      <h4 className={`font-semibold text-sm ${
                        isEnabled
                          ? 'text-green-700 dark:text-green-300'
                          : (resolvedTheme === 'dark' ? 'text-gray-100' : 'text-gray-800')
                      }`}>
                        {setting.label}
                      </h4>
                      <p className={`text-xs ${
                        isEnabled
                          ? 'text-green-600 dark:text-green-400'
                          : (resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600')
                      }`}>
                        {setting.description}
                      </p>
                    </div>
                  </div>

                  <button
                    onClick={() => setNotificationSettings(prev => ({
                      ...prev,
                      [setting.key]: !prev[setting.key as keyof NotificationSettings]
                    }))}
                    className={`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-green-500/20 ${
                      isEnabled
                        ? 'bg-green-600 shadow-lg shadow-green-500/30'
                        : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-5 w-5 transform rounded-full bg-white transition-all duration-300 shadow-lg ${
                        isEnabled
                          ? 'translate-x-6 scale-110'
                          : 'translate-x-1'
                      }`}
                    />
                    {isEnabled && (
                      <Check className="absolute left-1.5 h-3 w-3 text-green-600" />
                    )}
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-1.5 rounded-lg bg-gradient-to-r from-teal-500 to-cyan-600">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Quick Actions
            </h3>
            <p className="text-xs" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Manage all notifications at once
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setNotificationSettings({
              emailNotifications: true,
              pushNotifications: true,
              lowStockAlerts: true,
              paymentReminders: true,
              systemUpdates: true
            })}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <Check className="h-4 w-4" />
            <span>Enable All</span>
          </button>

          <button
            onClick={() => setNotificationSettings({
              emailNotifications: false,
              pushNotifications: false,
              lowStockAlerts: false,
              paymentReminders: false,
              systemUpdates: false
            })}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
          >
            <X className="h-4 w-4" />
            <span>Disable All</span>
          </button>
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-8">
      {/* Security Features */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-600">
            <Shield className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Security Features
            </h3>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Protect your store and customer data
            </p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Two-Factor Authentication */}
          <div className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 hover:shadow-lg ${
            securitySettings.twoFactorAuth
              ? 'border-green-500 shadow-md shadow-green-500/10'
              : 'border-gray-200 dark:border-gray-600 hover:border-green-300'
          }`}
          style={{
            backgroundColor: securitySettings.twoFactorAuth
              ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.05)' : 'rgba(34, 197, 94, 0.02)')
              : (resolvedTheme === 'dark' ? '#374151' : '#ffffff')
          }}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <Lock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h4 className={`font-semibold text-sm ${
                    securitySettings.twoFactorAuth
                      ? 'text-green-700 dark:text-green-300'
                      : (resolvedTheme === 'dark' ? 'text-gray-100' : 'text-gray-800')
                  }`}>
                    Two-Factor Authentication
                  </h4>
                  <p className={`text-xs ${
                    securitySettings.twoFactorAuth
                      ? 'text-green-600 dark:text-green-400'
                      : (resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600')
                  }`}>
                    Add an extra layer of security to your account
                  </p>
                  {securitySettings.twoFactorAuth && (
                    <div className="flex items-center space-x-2 mt-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">Active</span>
                    </div>
                  )}
                </div>
              </div>

              <button
                onClick={() => setSecuritySettings(prev => ({ ...prev, twoFactorAuth: !prev.twoFactorAuth }))}
                className={`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-green-500/20 ${
                  securitySettings.twoFactorAuth
                    ? 'bg-green-600 shadow-lg shadow-green-500/30'
                    : 'bg-gray-200 dark:bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-5 w-5 transform rounded-full bg-white transition-all duration-300 shadow-lg ${
                    securitySettings.twoFactorAuth
                      ? 'translate-x-6 scale-110'
                      : 'translate-x-1'
                  }`}
                />
                {securitySettings.twoFactorAuth && (
                  <Check className="absolute left-1.5 h-3 w-3 text-green-600" />
                )}
              </button>
            </div>
          </div>

          {/* Security Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-semibold" style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}>
                Session Timeout
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={securitySettings.sessionTimeout}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) || 30 }))}
                  min="5"
                  max="480"
                  className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-red-500/20 focus:border-red-500"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-sm text-gray-500">minutes</span>
                </div>
              </div>
              <p className="text-xs text-gray-500">Auto-logout after inactivity</p>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold" style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}>
                Max Login Attempts
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={securitySettings.loginAttempts}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, loginAttempts: parseInt(e.target.value) || 5 }))}
                  min="3"
                  max="10"
                  className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-red-500/20 focus:border-red-500"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-sm text-gray-500">attempts</span>
                </div>
              </div>
              <p className="text-xs text-gray-500">Account lockout threshold</p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Actions */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-600">
            <AlertTriangle className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Security Actions
            </h3>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Manage your account security
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-left group">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <Download className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-700 dark:text-blue-300">Export Data</h4>
                <p className="text-sm text-blue-600 dark:text-blue-400">Download your store data</p>
              </div>
            </div>
          </button>

          <button className="p-4 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 text-left group">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <Trash2 className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h4 className="font-medium text-red-700 dark:text-red-300">Delete Account</h4>
                <p className="text-sm text-red-600 dark:text-red-400">Permanently remove account</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  )

  const renderSystemSettings = () => (
    <div className="space-y-8">
      {/* System Information */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-r from-slate-500 to-gray-600">
            <Database className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              System Information
            </h3>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Current system status and details
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 rounded-2xl border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center">
                <Info className="h-5 w-5 text-white" />
              </div>
              <h4 className="font-semibold text-blue-700 dark:text-blue-300">Application Info</h4>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-400">Version:</span>
                <span className="font-medium text-blue-800 dark:text-blue-200">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-400">Last Updated:</span>
                <span className="font-medium text-blue-800 dark:text-blue-200">{new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-400">Environment:</span>
                <span className="font-medium text-blue-800 dark:text-blue-200">Production</span>
              </div>
            </div>
          </div>

          <div className="p-6 rounded-2xl border-2 border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 rounded-lg bg-green-500 flex items-center justify-center">
                <Database className="h-5 w-5 text-white" />
              </div>
              <h4 className="font-semibold text-green-700 dark:text-green-300">Services Status</h4>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-green-600 dark:text-green-400">Database:</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-green-800 dark:text-green-200">Connected</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-600 dark:text-green-400">Storage:</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-green-800 dark:text-green-200">Cloudinary</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-600 dark:text-green-400">API:</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-green-800 dark:text-green-200">Online</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Regional Settings */}
      <div className="relative">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600">
            <Globe className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Regional Settings
            </h3>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Configure timezone and language preferences
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Timezone
            </label>
            <div className="relative">
              <select
                value={storeSettings.timezone}
                onChange={(e) => setStoreSettings(prev => ({ ...prev, timezone: e.target.value }))}
                className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-indigo-500/20 focus:border-indigo-500 appearance-none"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <option value="Asia/Manila">🇵🇭 Asia/Manila (GMT+8)</option>
                <option value="UTC">🌍 UTC (GMT+0)</option>
                <option value="America/New_York">🇺🇸 America/New_York (GMT-5)</option>
              </select>
              <ChevronRight className="absolute right-3 top-1/2 transform -translate-y-1/2 rotate-90 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
            }}>
              Language
            </label>
            <div className="relative">
              <select
                value={storeSettings.language}
                onChange={(e) => setStoreSettings(prev => ({ ...prev, language: e.target.value }))}
                className="w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-indigo-500/20 focus:border-indigo-500 appearance-none"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <option value="en">🇺🇸 English</option>
                <option value="fil">🇵🇭 Filipino</option>
                <option value="es">🇪🇸 Español</option>
              </select>
              <ChevronRight className="absolute right-3 top-1/2 transform -translate-y-1/2 rotate-90 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'store':
        return renderStoreSettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return renderSecuritySettings()
      case 'system':
        return renderSystemSettings()
      default:
        return renderStoreSettings()
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center shadow-lg">
            <SettingsIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold" style={{
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
            }}>
              Settings
            </h1>
            <p className="text-sm" style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}>
              Configure your store preferences and system options
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          {[
            { label: 'Store Name', value: storeSettings.storeName, icon: Store, color: 'from-blue-500 to-cyan-600' },
            { label: 'Theme', value: resolvedTheme === 'dark' ? 'Dark Mode' : 'Light Mode', icon: resolvedTheme === 'dark' ? Moon : Sun, color: 'from-purple-500 to-pink-600' },
            { label: 'Notifications', value: Object.values(notificationSettings).filter(Boolean).length + '/5', icon: Bell, color: 'from-orange-500 to-red-600' },
            { label: '2FA Status', value: securitySettings.twoFactorAuth ? 'Enabled' : 'Disabled', icon: Shield, color: 'from-green-500 to-emerald-600' }
          ].map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="p-3 rounded-lg border shadow-sm" style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
              }}>
                <div className="flex items-center space-x-2">
                  <div className={`w-7 h-7 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center`}>
                    <Icon className="h-3.5 w-3.5 text-white" />
                  </div>
                  <div>
                    <p className="text-xs font-medium" style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}>
                      {stat.label}
                    </p>
                    <p className="text-sm font-semibold" style={{
                      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
                    }}>
                      {stat.value}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Settings Navigation */}
        <div className="lg:w-1/4">
          <div className="sticky top-8">
            <nav className="space-y-2">
              {settingsSections.map((section) => {
                const Icon = section.icon
                const isActive = activeSection === section.id

                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`group w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${
                      isActive
                        ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                    }`}
                    style={{
                      backgroundColor: isActive
                        ? undefined
                        : (resolvedTheme === 'dark' ? '#374151' : '#ffffff')
                    }}
                  >
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 ${
                      isActive
                        ? 'bg-white/20 group-hover:scale-110'
                        : 'bg-gray-100 dark:bg-gray-600 group-hover:scale-110'
                    }`}>
                      <Icon className={`h-4 w-4 ${
                        isActive ? 'text-white' : 'text-gray-600 dark:text-gray-300'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <div className={`font-semibold text-sm ${
                        isActive
                          ? 'text-white'
                          : (resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-700')
                      }`}>
                        {section.label}
                      </div>
                      <div className={`text-xs ${
                        isActive
                          ? 'text-white/80'
                          : 'text-gray-500'
                      }`}>
                        {section.description}
                      </div>
                    </div>
                    {isActive && (
                      <ChevronRight className="h-4 w-4 text-white" />
                    )}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:w-3/4">
          <div className="rounded-3xl border-2 shadow-xl overflow-hidden" style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}>
            {/* Header */}
            <div className="relative p-6 border-b-2" style={{
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%)'
                : 'linear-gradient(135deg, rgba(249, 250, 251, 0.95) 0%, rgba(243, 244, 246, 0.9) 100%)',
              borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
            }}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${
                    activeSection === 'store' ? 'from-blue-500 to-purple-600' :
                    activeSection === 'appearance' ? 'from-purple-500 to-pink-600' :
                    activeSection === 'notifications' ? 'from-orange-500 to-red-600' :
                    activeSection === 'security' ? 'from-red-500 to-pink-600' :
                    'from-slate-500 to-gray-600'
                  } flex items-center justify-center shadow-lg`}>
                    {(() => {
                      const section = settingsSections.find(s => s.id === activeSection)
                      const Icon = section?.icon || Store
                      return <Icon className="h-5 w-5 text-white" />
                    })()}
                  </div>
                  <div>
                    <h2 className="text-xl font-bold" style={{
                      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                    }}>
                      {settingsSections.find(s => s.id === activeSection)?.label}
                    </h2>
                    <p className="text-xs mt-1" style={{
                      color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                    }}>
                      {settingsSections.find(s => s.id === activeSection)?.description}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="p-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 group"
                    title="Refresh Settings"
                  >
                    <RefreshCw className="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 group-hover:rotate-180 transition-all duration-300" />
                  </button>

                  <button
                    onClick={saveSettings}
                    disabled={isLoading}
                    className="flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                  >
                    {isLoading ? (
                      <RefreshCw className="h-5 w-5 animate-spin" />
                    ) : (
                      <Save className="h-5 w-5" />
                    )}
                    <span className="font-semibold">{isLoading ? 'Saving...' : 'Save Changes'}</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="animate-fade-in">
                {renderContent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
